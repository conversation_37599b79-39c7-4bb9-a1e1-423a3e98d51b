<!DOCTYPE html>
<html lang="pl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Discord Bybit Signal Monitor - Dashboard</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <style>
      .card-stat {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
      }
      .card-stat.positive {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
      }
      .card-stat.negative {
        background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
      }
      .signal-row.positive {
        background-color: rgba(40, 167, 69, 0.1);
      }
      .signal-row.negative {
        background-color: rgba(220, 53, 69, 0.1);
      }
      .status-badge {
        font-size: 0.8em;
      }
      .chart-container {
        position: relative;
        height: 400px;
      }
      .live-indicator {
        display: inline-block;
        width: 10px;
        height: 10px;
        background-color: #28a745;
        border-radius: 50%;
        animation: pulse 2s infinite;
      }
      @keyframes pulse {
        0% {
          opacity: 1;
        }
        50% {
          opacity: 0.5;
        }
        100% {
          opacity: 1;
        }
      }
    </style>
  </head>
  <body class="bg-light">
    <nav class="navbar navbar-dark bg-dark">
      <div class="container-fluid">
        <span class="navbar-brand mb-0 h1">
          <i class="fas fa-chart-line"></i>
          Discord Bybit Signal Monitor
          <span class="live-indicator ms-2" id="liveIndicator"></span>
          <small class="text-muted">Live</small>
        </span>
        <button class="btn btn-outline-light btn-sm" onclick="refreshData()">
          <i class="fas fa-sync-alt"></i> Odśwież
        </button>
      </div>
    </nav>

    <div class="container-fluid mt-4">
      <!-- Filtry czasowe -->
      <div class="row mb-3">
        <div class="col-md-12">
          <div class="card">
            <div class="card-body">
              <div class="row align-items-center">
                <div class="col-md-6">
                  <h6 class="mb-0">Okres analizy:</h6>
                </div>
                <div class="col-md-6">
                  <div
                    class="btn-group w-100"
                    role="group"
                    id="timeFilterButtons"
                  >
                    <button
                      type="button"
                      class="btn btn-outline-primary active"
                      onclick="setTimeFilter(null)"
                    >
                      Wszystko
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-primary"
                      onclick="setTimeFilter(1)"
                    >
                      24h
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-primary"
                      onclick="setTimeFilter(7)"
                    >
                      7 dni
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-primary"
                      onclick="setTimeFilter(30)"
                    >
                      30 dni
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Podstawowe statystyki -->
      <div class="row mb-4">
        <div class="col-md-3">
          <div class="card card-stat">
            <div class="card-body text-center">
              <i class="fas fa-signal fa-2x mb-2"></i>
              <h4 id="totalSignals">-</h4>
              <p class="mb-0">Wszystkie sygnały</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card card-stat positive">
            <div class="card-body text-center">
              <i class="fas fa-trophy fa-2x mb-2"></i>
              <h4 id="winRate">-</h4>
              <p class="mb-0">Win Rate</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card card-stat">
            <div class="card-body text-center">
              <i class="fas fa-percentage fa-2x mb-2"></i>
              <h4 id="avgPnl">-</h4>
              <p class="mb-0">Średni PnL</p>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card card-stat">
            <div class="card-body text-center">
              <i class="fas fa-dollar-sign fa-2x mb-2"></i>
              <h4 id="totalPnl">-</h4>
              <p class="mb-0">Całkowity PnL</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Zaawansowane metryki -->
      <div class="row mb-4">
        <div class="col-md-2">
          <div class="card card-stat">
            <div class="card-body text-center">
              <i class="fas fa-chart-line fa-lg mb-2"></i>
              <h6 id="sharpeRatio">-</h6>
              <small class="mb-0">Sharpe Ratio</small>
            </div>
          </div>
        </div>
        <div class="col-md-2">
          <div class="card card-stat negative">
            <div class="card-body text-center">
              <i class="fas fa-arrow-down fa-lg mb-2"></i>
              <h6 id="maxDrawdown">-</h6>
              <small class="mb-0">Max Drawdown</small>
            </div>
          </div>
        </div>
        <div class="col-md-2">
          <div class="card card-stat positive">
            <div class="card-body text-center">
              <i class="fas fa-fire fa-lg mb-2"></i>
              <h6 id="maxWins">-</h6>
              <small class="mb-0">Max Wins</small>
            </div>
          </div>
        </div>
        <div class="col-md-2">
          <div class="card card-stat negative">
            <div class="card-body text-center">
              <i class="fas fa-snowflake fa-lg mb-2"></i>
              <h6 id="maxLosses">-</h6>
              <small class="mb-0">Max Losses</small>
            </div>
          </div>
        </div>
        <div class="col-md-2">
          <div class="card card-stat">
            <div class="card-body text-center">
              <i class="fas fa-balance-scale fa-lg mb-2"></i>
              <h6 id="profitFactor">-</h6>
              <small class="mb-0">Profit Factor</small>
            </div>
          </div>
        </div>
        <div class="col-md-2">
          <div class="card card-stat">
            <div class="card-body text-center">
              <i class="fas fa-clock fa-lg mb-2"></i>
              <h6 id="openSignals">-</h6>
              <small class="mb-0">Otwarte</small>
            </div>
          </div>
        </div>
      </div>

      <!-- Wykresy -->
      <div class="row mb-4">
        <div class="col-md-8">
          <div class="card">
            <div class="card-header">
              <h5>
                <i class="fas fa-chart-line"></i> Equity Curve (Krzywa kapitału)
              </h5>
            </div>
            <div class="card-body">
              <div class="chart-container">
                <canvas id="pnlChart"></canvas>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card">
            <div class="card-header">
              <h5><i class="fas fa-pie-chart"></i> Rozkład wyników</h5>
            </div>
            <div class="card-body">
              <div class="chart-container">
                <canvas id="statusChart"></canvas>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Dodatkowe wykresy -->
      <div class="row mb-4">
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h5><i class="fas fa-chart-bar"></i> Rozkład PnL</h5>
            </div>
            <div class="card-body">
              <div class="chart-container">
                <canvas id="pnlDistributionChart"></canvas>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h5><i class="fas fa-coins"></i> Wydajność per para</h5>
            </div>
            <div class="card-body">
              <div class="chart-container">
                <canvas id="pairPerformanceChart"></canvas>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Statystyki per timeframe -->
      <div class="row mb-4">
        <div class="col-md-12">
          <div class="card">
            <div class="card-header">
              <h5><i class="fas fa-clock"></i> Analiza per timeframe</h5>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-sm">
                  <thead class="table-dark">
                    <tr>
                      <th>Timeframe</th>
                      <th>Liczba sygnałów</th>
                      <th>Wygrane</th>
                      <th>Win Rate</th>
                      <th>Średni PnL</th>
                    </tr>
                  </thead>
                  <tbody id="timeframeStatsTable">
                    <tr>
                      <td colspan="5" class="text-center">Ładowanie...</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Filtry -->
      <div class="row mb-3">
        <div class="col-md-12">
          <div class="card">
            <div class="card-body">
              <div class="row">
                <div class="col-md-3">
                  <label for="statusFilter" class="form-label">Status:</label>
                  <select
                    class="form-select"
                    id="statusFilter"
                    onchange="filterSignals()"
                  >
                    <option value="all">Wszystkie</option>
                    <option value="NEW">Nowe</option>
                    <option value="ENTRY_HIT">Entry Hit</option>
                    <option value="TP_HIT">Take Profit</option>
                    <option value="SL_HIT">Stop Loss</option>
                    <option value="EXPIRED">Wygasłe</option>
                  </select>
                </div>
                <div class="col-md-3">
                  <label for="pairFilter" class="form-label">Para:</label>
                  <select
                    class="form-select"
                    id="pairFilter"
                    onchange="filterSignals()"
                  >
                    <option value="all">Wszystkie</option>
                  </select>
                </div>
                <div class="col-md-3">
                  <label for="limitFilter" class="form-label">Limit:</label>
                  <select
                    class="form-select"
                    id="limitFilter"
                    onchange="filterSignals()"
                  >
                    <option value="">Wszystkie</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                    <option value="500">500</option>
                  </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                  <button class="btn btn-primary w-100" onclick="exportData()">
                    <i class="fas fa-download"></i> Eksport CSV
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Tabela sygnałów -->
      <div class="row">
        <div class="col-md-12">
          <div class="card">
            <div
              class="card-header d-flex justify-content-between align-items-center"
            >
              <h5><i class="fas fa-table"></i> Sygnały</h5>
              <span class="badge bg-primary" id="signalCount">0</span>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-hover">
                  <thead class="table-dark">
                    <tr>
                      <th>ID</th>
                      <th>Para</th>
                      <th>Kierunek</th>
                      <th>Entry</th>
                      <th>TP</th>
                      <th>SL</th>
                      <th>Status</th>
                      <th>PnL</th>
                      <th>Data</th>
                    </tr>
                  </thead>
                  <tbody id="signalsTable">
                    <tr>
                      <td colspan="9" class="text-center">
                        <div class="spinner-border" role="status">
                          <span class="visually-hidden">Ładowanie...</span>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
      // Inicjalizacja Socket.IO
      const socket = io();

      // Zmienne globalne
      let pnlChart, statusChart, pnlDistributionChart, pairPerformanceChart;
      let currentSignals = [];
      let currentTimeFilter = null;

      // Inicjalizacja po załadowaniu strony
      document.addEventListener("DOMContentLoaded", function () {
        initCharts();
        loadData();
        loadPairs();
      });

      // Socket.IO event handlers
      socket.on("connect", function () {
        console.log("Connected to server");
        document.getElementById("liveIndicator").style.backgroundColor =
          "#28a745";
      });

      socket.on("disconnect", function () {
        console.log("Disconnected from server");
        document.getElementById("liveIndicator").style.backgroundColor =
          "#dc3545";
      });

      socket.on("new_signal", function (data) {
        console.log("New signal received:", data);
        loadData(); // Odśwież dane
      });

      socket.on("signal_update", function (data) {
        console.log("Signal updated:", data);
        loadData(); // Odśwież dane
      });

      // Funkcje główne
      async function loadData() {
        try {
          await Promise.all([
            loadStatistics(),
            loadSignals(),
            loadPnlChart(),
            loadPnlDistribution(),
            loadPairPerformance(),
            loadPerformanceMetrics(),
          ]);
        } catch (error) {
          console.error("Error loading data:", error);
        }
      }

      function setTimeFilter(days) {
        currentTimeFilter = days;

        // Aktualizuj aktywny przycisk
        document.querySelectorAll("#timeFilterButtons .btn").forEach((btn) => {
          btn.classList.remove("active");
        });

        if (days === null) {
          document
            .querySelector("#timeFilterButtons .btn:first-child")
            .classList.add("active");
        } else {
          document
            .querySelector(
              `#timeFilterButtons .btn[onclick="setTimeFilter(${days})"]`
            )
            .classList.add("active");
        }

        // Przeładuj dane
        loadData();
      }

      async function loadStatistics() {
        const params = new URLSearchParams();
        if (currentTimeFilter) params.append("days", currentTimeFilter);

        const response = await fetch("/api/statistics?" + params.toString());
        const stats = await response.json();

        // Podstawowe statystyki
        document.getElementById("totalSignals").textContent =
          stats.total_signals;
        document.getElementById("winRate").textContent =
          stats.win_rate.toFixed(1) + "%";
        document.getElementById("avgPnl").textContent =
          (stats.avg_pnl * 100).toFixed(2) + "%";
        document.getElementById("totalPnl").textContent =
          (stats.total_pnl * 100).toFixed(2) + "%";
        document.getElementById("openSignals").textContent =
          stats.total_signals - stats.closed_signals;

        // Zaawansowane metryki
        document.getElementById("sharpeRatio").textContent =
          stats.sharpe_ratio.toFixed(2);
        document.getElementById("maxDrawdown").textContent =
          (stats.max_drawdown * 100).toFixed(2) + "%";
        document.getElementById("maxWins").textContent =
          stats.max_consecutive_wins;
        document.getElementById("maxLosses").textContent =
          stats.max_consecutive_losses;
        document.getElementById("profitFactor").textContent =
          stats.profit_factor === Infinity
            ? "∞"
            : stats.profit_factor.toFixed(2);

        // Aktualizuj wykres statusów
        updateStatusChart(stats);

        // Aktualizuj tabelę timeframe
        updateTimeframeTable(stats.timeframe_stats);
      }

      async function loadSignals() {
        const params = new URLSearchParams();
        const status = document.getElementById("statusFilter").value;
        const pair = document.getElementById("pairFilter").value;
        const limit = document.getElementById("limitFilter").value;

        if (status !== "all") params.append("status", status);
        if (pair !== "all") params.append("pair", pair);
        if (limit) params.append("limit", limit);

        const response = await fetch("/api/signals?" + params.toString());
        const signals = await response.json();

        currentSignals = signals;
        updateSignalsTable(signals);
      }

      async function loadPnlChart() {
        const params = new URLSearchParams();
        if (currentTimeFilter) params.append("days", currentTimeFilter);

        const response = await fetch("/api/pnl-chart?" + params.toString());
        const data = await response.json();
        updatePnlChart(data);
      }

      async function loadPnlDistribution() {
        try {
          const response = await fetch("/api/pnl-distribution");
          const data = await response.json();
          updatePnlDistributionChart(data);
        } catch (error) {
          console.error("Error loading PnL distribution:", error);
        }
      }

      async function loadPairPerformance() {
        try {
          const params = new URLSearchParams();
          if (currentTimeFilter) params.append("days", currentTimeFilter);

          const response = await fetch("/api/statistics?" + params.toString());
          const stats = await response.json();
          updatePairPerformanceChart(stats.pair_stats);
        } catch (error) {
          console.error("Error loading pair performance:", error);
        }
      }

      async function loadPerformanceMetrics() {
        try {
          const params = new URLSearchParams();
          if (currentTimeFilter) params.append("days", currentTimeFilter);

          const response = await fetch(
            "/api/performance-metrics?" + params.toString()
          );
          const data = await response.json();
          // Dane już są aktualizowane w loadStatistics
        } catch (error) {
          console.error("Error loading performance metrics:", error);
        }
      }

      async function loadPairs() {
        const response = await fetch("/api/pairs");
        const pairs = await response.json();

        const select = document.getElementById("pairFilter");
        // Zachowaj opcję "Wszystkie"
        const currentValue = select.value;
        select.innerHTML = '<option value="all">Wszystkie</option>';

        pairs.forEach((pair) => {
          const option = document.createElement("option");
          option.value = pair;
          option.textContent = pair;
          select.appendChild(option);
        });

        select.value = currentValue;
      }

      function initCharts() {
        // Wykres PnL
        const pnlCtx = document.getElementById("pnlChart").getContext("2d");
        pnlChart = new Chart(pnlCtx, {
          type: "line",
          data: {
            labels: [],
            datasets: [
              {
                label: "Kumulatywny PnL",
                data: [],
                borderColor: "rgb(75, 192, 192)",
                backgroundColor: "rgba(75, 192, 192, 0.2)",
                tension: 0.1,
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: true,
                ticks: {
                  callback: function (value) {
                    return (value * 100).toFixed(2) + "%";
                  },
                },
              },
            },
          },
        });

        // Wykres statusów
        const statusCtx = document
          .getElementById("statusChart")
          .getContext("2d");
        statusChart = new Chart(statusCtx, {
          type: "doughnut",
          data: {
            labels: ["Take Profit", "Stop Loss", "Timeout", "Otwarte"],
            datasets: [
              {
                data: [0, 0, 0, 0],
                backgroundColor: ["#28a745", "#dc3545", "#ffc107", "#6c757d"],
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
          },
        });

        // Wykres rozkładu PnL
        const pnlDistCtx = document
          .getElementById("pnlDistributionChart")
          .getContext("2d");
        pnlDistributionChart = new Chart(pnlDistCtx, {
          type: "bar",
          data: {
            labels: [],
            datasets: [
              {
                label: "Liczba sygnałów",
                data: [],
                backgroundColor: "rgba(54, 162, 235, 0.6)",
                borderColor: "rgba(54, 162, 235, 1)",
                borderWidth: 1,
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: true,
                ticks: {
                  stepSize: 1,
                },
              },
            },
            plugins: {
              legend: {
                display: false,
              },
            },
          },
        });

        // Wykres wydajności per para
        const pairPerfCtx = document
          .getElementById("pairPerformanceChart")
          .getContext("2d");
        pairPerformanceChart = new Chart(pairPerfCtx, {
          type: "bar",
          data: {
            labels: [],
            datasets: [
              {
                label: "Całkowity PnL",
                data: [],
                backgroundColor: function (context) {
                  const value = context.parsed.y;
                  return value >= 0
                    ? "rgba(40, 167, 69, 0.6)"
                    : "rgba(220, 53, 69, 0.6)";
                },
                borderColor: function (context) {
                  const value = context.parsed.y;
                  return value >= 0
                    ? "rgba(40, 167, 69, 1)"
                    : "rgba(220, 53, 69, 1)";
                },
                borderWidth: 1,
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: true,
                ticks: {
                  callback: function (value) {
                    return (value * 100).toFixed(1) + "%";
                  },
                },
              },
            },
            plugins: {
              legend: {
                display: false,
              },
            },
          },
        });
      }

      function updatePnlChart(data) {
        const labels = data.map((d) => d.date);
        const pnlData = data.map((d) => d.cumulative_pnl);

        pnlChart.data.labels = labels;
        pnlChart.data.datasets[0].data = pnlData;
        pnlChart.update();
      }

      function updateStatusChart(stats) {
        statusChart.data.datasets[0].data = [
          stats.tp_hits,
          stats.sl_hits,
          stats.timeouts,
          stats.total_signals - stats.closed_signals,
        ];
        statusChart.update();
      }

      function updatePnlDistributionChart(data) {
        try {
          if (!pnlDistributionChart) {
            console.warn("PnL Distribution chart not initialized");
            return;
          }

          if (!data || data.length === 0) {
            pnlDistributionChart.data.labels = [];
            pnlDistributionChart.data.datasets[0].data = [];
            pnlDistributionChart.update();
            return;
          }

          const labels = data.map(
            (d) =>
              `${(d.range_start * 100).toFixed(1)} - ${(
                d.range_end * 100
              ).toFixed(1)}`
          );
          const counts = data.map((d) => d.count);

          pnlDistributionChart.data.labels = labels;
          pnlDistributionChart.data.datasets[0].data = counts;
          pnlDistributionChart.update();
        } catch (error) {
          console.error("Error updating PnL distribution chart:", error);
        }
      }

      function updatePairPerformanceChart(pairStats) {
        try {
          if (!pairPerformanceChart) {
            console.warn("Pair Performance chart not initialized");
            return;
          }

          if (!pairStats || pairStats.length === 0) {
            pairPerformanceChart.data.labels = [];
            pairPerformanceChart.data.datasets[0].data = [];
            pairPerformanceChart.update();
            return;
          }

          const labels = pairStats.map((p) => p.pair);
          const pnlData = pairStats.map((p) => p.total_pnl || 0);

          pairPerformanceChart.data.labels = labels;
          pairPerformanceChart.data.datasets[0].data = pnlData;
          pairPerformanceChart.update();
        } catch (error) {
          console.error("Error updating pair performance chart:", error);
        }
      }

      function updateTimeframeTable(timeframeStats) {
        const tbody = document.getElementById("timeframeStatsTable");

        if (!timeframeStats || timeframeStats.length === 0) {
          tbody.innerHTML =
            '<tr><td colspan="5" class="text-center">Brak danych</td></tr>';
          return;
        }

        tbody.innerHTML = timeframeStats
          .map((tf) => {
            const winRate =
              tf.count > 0 ? ((tf.wins / tf.count) * 100).toFixed(1) : "0.0";
            const avgPnl = tf.avg_pnl ? (tf.avg_pnl * 100).toFixed(2) : "0.00";

            return `
            <tr>
              <td><strong>${tf.timeframe_min}m</strong></td>
              <td>${tf.count}</td>
              <td>${tf.wins}</td>
              <td>${winRate}%</td>
              <td>${avgPnl}%</td>
            </tr>
          `;
          })
          .join("");
      }

      function updateSignalsTable(signals) {
        const tbody = document.getElementById("signalsTable");
        document.getElementById("signalCount").textContent = signals.length;

        if (signals.length === 0) {
          tbody.innerHTML =
            '<tr><td colspan="9" class="text-center">Brak sygnałów</td></tr>';
          return;
        }

        tbody.innerHTML = signals
          .map((signal) => {
            const pnlClass =
              signal.pnl > 0 ? "positive" : signal.pnl < 0 ? "negative" : "";
            const statusBadge = getStatusBadge(signal.status);

            return `
                    <tr class="signal-row ${pnlClass}">
                        <td>${signal.id}</td>
                        <td><strong>${signal.pair}</strong></td>
                        <td><span class="badge ${
                          signal.side === "BUY" ? "bg-success" : "bg-danger"
                        }">${signal.side}</span></td>
                        <td>${signal.entry}</td>
                        <td>${signal.tp}</td>
                        <td>${signal.sl}</td>
                        <td>${statusBadge}</td>
                        <td>${signal.pnl_percent || "-"}</td>
                        <td>${signal.timestamp}</td>
                    </tr>
                `;
          })
          .join("");
      }

      function getStatusBadge(status) {
        const badges = {
          NEW: '<span class="badge bg-primary status-badge">Nowy</span>',
          ENTRY_HIT:
            '<span class="badge bg-info status-badge">Entry Hit</span>',
          TP_HIT: '<span class="badge bg-success status-badge">TP Hit</span>',
          SL_HIT: '<span class="badge bg-danger status-badge">SL Hit</span>',
          EXPIRED: '<span class="badge bg-warning status-badge">Wygasły</span>',
          // Backward compatibility
          open: '<span class="badge bg-secondary status-badge">Otwarte</span>',
          tp: '<span class="badge bg-success status-badge">TP</span>',
          sl: '<span class="badge bg-danger status-badge">SL</span>',
          timeout: '<span class="badge bg-warning status-badge">Timeout</span>',
        };
        return badges[status] || status;
      }

      function filterSignals() {
        loadSignals();
      }

      function refreshData() {
        loadData();
      }

      function exportData() {
        if (currentSignals.length === 0) {
          alert("Brak danych do eksportu");
          return;
        }

        const csv = convertToCSV(currentSignals);
        const blob = new Blob([csv], { type: "text/csv" });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `signals_${new Date().toISOString().split("T")[0]}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
      }

      function convertToCSV(data) {
        const headers = [
          "ID",
          "Para",
          "Kierunek",
          "Entry",
          "TP",
          "SL",
          "Status",
          "PnL",
          "Data",
        ];
        const rows = data.map((signal) => [
          signal.id,
          signal.pair,
          signal.side,
          signal.entry,
          signal.tp,
          signal.sl,
          signal.status,
          signal.pnl || "",
          signal.timestamp,
        ]);

        return [headers, ...rows].map((row) => row.join(",")).join("\n");
      }

      // Auto-refresh co 30 sekund
      setInterval(loadData, 30000);
    </script>
  </body>
</html>
